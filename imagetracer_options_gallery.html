<!DOCTYPE html>
<html>

<head>
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
	<meta charset="utf-8">
	<title>imagetracer.js options gallery</title>
	<style>
	
		body {
			zoom: 3; 
			-moz-transform: scale(3); 
			-moz-transform-origin: 0 0;
		} 
	
		img, svg, canvas {
			background:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAECAYAAACp8Z5+AAAAIklEQVQImWPQ1tb+r62t/d/f3/+/v7//fwYMARgDJoEhAACRARthAfQS8AAAAABJRU5ErkJggg==) repeat;
			border: solid 1px cyan;
			image-rendering: optimizeSpeed;             /* Older versions of FF          */
			image-rendering: -moz-crisp-edges;          /* FF 6.0+                       */
			image-rendering: -webkit-optimize-contrast; /* Safari                        */
			image-rendering: -o-crisp-edges;            /* OS X & Windows Opera (12.02+) */
			image-rendering: pixelated;                 /* Awesome future-browsers       */
			-ms-interpolation-mode: nearest-neighbor;   /* IE                            */
		}
		
		table{ border: solid 1px gray; border-collapse:collapse; }
		td{ border: solid 1px gray; vertical-align:top; }
		th{ border: solid 1px gray; }
		
	</style>
	<script src="imagetracer_v1.2.6.js"></script>
	<script>
		
		var cnt = 0;
	
		function onload_init(){
			tracenext();
		}
		
		function tracenext(){
			if(cnt<16){
				ImageTracer.imageToSVG('testimages/combined.png', 
					function(svgstr){
						document.getElementById('td'+(cnt+1)).innerHTML = svgstr +'<br/>'+Object.keys(ImageTracer.optionpresets)[cnt]+'  '+svgstr.length+' bytes';
						cnt++;
						tracenext();
					},
					Object.keys(ImageTracer.optionpresets)[cnt]
				);
			}
		}
		
	</script>
</head>

<body style="background-color:rgb(32,32,32);color:rgb(255,255,255);font-family:monospace;" onload="onload_init()" >
	<noscript style="background-color:rgb(255,0,0);color:rgb(255,255,255);font-size: 250%;">Please enable JavaScript!</noscript>
	<div id="content">
	<table><tbody>
	<tr id="tr0" ><td id="td0"><img src="testimages/combined.png" /><br>Original</td><td id="td1"></td><td id="td2"></td><td id="td3"></td><td id="td16"></td></tr>
	<tr id="tr1" ><td id="td4"></td><td id="td5"></td><td id="td6"></td><td id="td7"></td><td id="td17"></td></tr>
	<tr id="tr2" ><td id="td8"></td><td id="td9"></td><td id="td10"></td><td id="td11"></td><td id="td18"></td></tr>
	<tr id="tr3" ><td id="td12"></td><td id="td13"></td><td id="td14"></td><td id="td15"></td><td id="td19"></td></tr>
	</tbody></table>
	</div>
</body>

</html>