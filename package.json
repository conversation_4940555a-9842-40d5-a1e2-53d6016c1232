{"name": "imagetracerjs", "version": "1.2.6", "description": "raster image tracer and vectorizer, bitmap to SVG converter", "main": "imagetracer_v1.2.6.js", "scripts": {"test": "node ./nodecli/nodecli.js"}, "repository": {"type": "git", "url": "git+https://github.com/jankovicsandras/imagetracerjs.git"}, "keywords": ["image", "tracer", "tracing", "vector", "raster", "vectorize", "vectorizing", "convert", "conversion", "converting", "tracing", "bitmap", "svg", "bmp", "png", "jpg", "jpeg", "gif"], "author": "<PERSON><PERSON><PERSON>", "license": "Unlicense", "bugs": {"url": "https://github.com/jan<PERSON>sandras/imagetracerjs/issues"}, "homepage": "https://github.com/jan<PERSON>sandras/imagetracerjs#readme"}